#!/bin/bash

# 寸止工具Docker运行脚本

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 构建Docker镜像
echo "🔨 构建寸止工具Docker镜像..."
docker build -f Dockerfile.cunzhi -t cunzhi-tools .

if [ $? -ne 0 ]; then
    echo "❌ Docker镜像构建失败"
    exit 1
fi

echo "✅ Docker镜像构建成功"

# 运行寸止工具
echo "🚀 启动寸止工具容器..."
docker run -it --rm \
    -v "$(pwd):/workspace" \
    -w /workspace \
    cunzhi-tools \
    /app/寸止 "$@"
